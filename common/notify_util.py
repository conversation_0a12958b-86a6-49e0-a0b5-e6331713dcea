import requests

# https://api.day.app/Whsvw4oo4AFPcUZoQCWedH/xxx

push_url = 'https://api.day.app/Whsvw4oo4AFPcUZoQCWedH/'


def send_notify(title, body):
    params_json = {
        'title': title,
        'body': body
    }
    try:
        r = requests.post(url=push_url, json=params_json)
        r.raise_for_status()  # 处理异常http返回
    except requests.exceptions.RequestException as e:
        print("发送通知时出现异常:", e)


if __name__ == "__main__":
    send_notify('title', 'body')

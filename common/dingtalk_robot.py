#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简化的钉钉机器人工具类
只保留核心的文本消息发送功能
"""

import time
import hmac
import hashlib
import base64
import urllib.parse
import requests
from typing import List, Optional, Dict, Any


class DingTalkRobot:
    """
    简化的钉钉自定义机器人客户端

    使用示例:
        robot = DingTalkRobot("your_token", "your_secret")
        robot.send_text("Hello, DingTalk!")
        robot.send_text("重要通知", at_all=True)
        robot.send_text("请查看", at_mobiles=["13800138000"])
    """

    def __init__(self, access_token: str, secret: str):
        """
        初始化钉钉机器人

        Args:
            access_token: 机器人webhook的access_token
            secret: 机器人安全设置的加签secret
        """
        self.access_token = access_token
        self.secret = secret
        self.base_url = "https://oapi.dingtalk.com/robot/send"

    def _generate_sign(self) -> tuple:
        """生成钉钉机器人签名"""
        timestamp = str(round(time.time() * 1000))
        string_to_sign = f'{timestamp}\n{self.secret}'
        hmac_code = hmac.new(
            self.secret.encode('utf-8'),
            string_to_sign.encode('utf-8'),
            digestmod=hashlib.sha256
        ).digest()
        sign = urllib.parse.quote_plus(base64.b64encode(hmac_code))
        return timestamp, sign

    def send_text(self, content: str,
                  at_user_ids: Optional[List[str]] = None,
                  at_mobiles: Optional[List[str]] = None,
                  at_all: bool = False) -> Dict[str, Any]:
        """
        发送文本消息

        Args:
            content: 消息内容
            at_user_ids: @的用户ID列表
            at_mobiles: @的手机号列表
            at_all: 是否@所有人

        Returns:
            dict: 钉钉API响应
        """
        timestamp, sign = self._generate_sign()
        url = f"{self.base_url}?access_token={self.access_token}&timestamp={timestamp}&sign={sign}"

        message_data = {
            "msgtype": "text",
            "text": {
                "content": content
            },
            "at": {
                "isAtAll": at_all,
                "atUserIds": at_user_ids or [],
                "atMobiles": at_mobiles or []
            }
        }

        headers = {'Content-Type': 'application/json'}
        response = requests.post(url, json=message_data, headers=headers)
        return response.json()



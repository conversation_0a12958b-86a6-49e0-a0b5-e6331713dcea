#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OSS集成模块
功能：
1. 集成现有的OSS工具类
2. 提供图片上传接口
3. 处理上传错误和重试
"""

import os
import logging
from typing import Optional, Dict, Any
import time

logger = logging.getLogger(__name__)


class OSSUploader:
    """OSS上传器"""
    
    def __init__(self):
        self.upload_success_count = 0
        self.upload_error_count = 0
        self.upload_errors = []
        
        # 检查OSS配置是否可用
        self.oss_available = self._check_oss_availability()
    
    def _check_oss_availability(self) -> bool:
        """检查OSS是否可用"""
        try:
            # 尝试导入OSS相关模块
            from common.oss_util import upload_one_img_to_oss_by_data
            from common.configs import current_config
            
            # 检查配置是否存在
            if hasattr(current_config, 'OSS_ACCESS_KEY_ID') and hasattr(current_config, 'OSS_ACCESS_KEY_SECRET'):
                if current_config.OSS_ACCESS_KEY_ID and current_config.OSS_ACCESS_KEY_SECRET:
                    logger.info("OSS配置检查通过，将使用真实OSS上传")
                    return True
            
            logger.warning("OSS配置不完整，将使用模拟上传")
            return False
            
        except ImportError as e:
            logger.warning(f"OSS模块导入失败，将使用模拟上传: {e}")
            return False
        except Exception as e:
            logger.warning(f"OSS可用性检查失败，将使用模拟上传: {e}")
            return False
    
    def upload_image_data(self, image_data: bytes, file_extension: str = 'jpg') -> Optional[str]:
        """上传图片数据到OSS"""
        try:
            if self.oss_available:
                return self._upload_to_real_oss(image_data, file_extension)
            else:
                return self._upload_to_mock_oss(image_data, file_extension)
                
        except Exception as e:
            logger.error(f"上传图片时出错: {e}")
            self.upload_error_count += 1
            self.upload_errors.append({
                'error': str(e),
                'timestamp': time.time(),
                'data_size': len(image_data) if image_data else 0
            })
            return None
    
    def _upload_to_real_oss(self, image_data: bytes, file_extension: str) -> Optional[str]:
        """上传到真实的OSS"""
        try:
            from common.oss_util import upload_one_img_to_oss_by_data
            
            logger.info(f"开始上传图片到OSS，大小: {len(image_data)} bytes")
            
            # 调用现有的OSS上传函数
            oss_filename = upload_one_img_to_oss_by_data(image_data, file_extension)
            
            if oss_filename:
                # 构建完整的URL
                from common.configs import current_config
                base_url = getattr(current_config, 'image_server_url', 'https://images2.kkzhw.com/')
                if not base_url.endswith('/'):
                    base_url += '/'
                
                full_url = base_url + oss_filename
                
                self.upload_success_count += 1
                logger.info(f"OSS上传成功: {full_url}")
                return full_url
            else:
                logger.error("OSS上传失败，返回空文件名")
                self.upload_error_count += 1
                return None
                
        except Exception as e:
            logger.error(f"真实OSS上传失败: {e}")
            self.upload_error_count += 1
            raise
    
    def _upload_to_mock_oss(self, image_data: bytes, file_extension: str) -> Optional[str]:
        """模拟OSS上传"""
        try:
            import hashlib
            
            # 生成模拟的文件名
            md5_hash = hashlib.md5(image_data).hexdigest()
            timestamp = int(time.time())
            
            # 模拟OSS路径结构
            date_path = time.strftime("%Y%m%d", time.localtime(timestamp))
            filename = f"{md5_hash[:8]}_{timestamp}.{file_extension}"
            
            mock_url = f"https://images2.kkzhw.com/mall/images2/{date_path}/{filename}"
            
            self.upload_success_count += 1
            logger.info(f"模拟OSS上传成功: {mock_url} (大小: {len(image_data)} bytes)")
            return mock_url
            
        except Exception as e:
            logger.error(f"模拟OSS上传失败: {e}")
            self.upload_error_count += 1
            raise
    
    def upload_image_file(self, file_path: str) -> Optional[str]:
        """上传图片文件到OSS"""
        try:
            if not os.path.exists(file_path):
                logger.error(f"图片文件不存在: {file_path}")
                return None
            
            # 读取文件数据
            with open(file_path, 'rb') as f:
                image_data = f.read()
            
            # 获取文件扩展名
            file_extension = os.path.splitext(file_path)[1].lstrip('.').lower()
            if not file_extension:
                file_extension = 'jpg'
            
            return self.upload_image_data(image_data, file_extension)
            
        except Exception as e:
            logger.error(f"上传图片文件时出错: {e}")
            self.upload_error_count += 1
            return None
    
    def batch_upload_with_retry(self, upload_tasks: list, max_retries: int = 3) -> Dict[str, Any]:
        """批量上传，支持重试"""
        results = {}
        
        for task_id, task_data in upload_tasks:
            success = False
            last_error = None
            
            for attempt in range(max_retries):
                try:
                    if 'file_path' in task_data:
                        url = self.upload_image_file(task_data['file_path'])
                    elif 'image_data' in task_data:
                        url = self.upload_image_data(
                            task_data['image_data'], 
                            task_data.get('file_extension', 'jpg')
                        )
                    else:
                        raise ValueError("任务数据格式错误")
                    
                    if url:
                        results[task_id] = {'success': True, 'url': url}
                        success = True
                        break
                    else:
                        raise Exception("上传返回空URL")
                        
                except Exception as e:
                    last_error = e
                    if attempt < max_retries - 1:
                        wait_time = (attempt + 1) * 2  # 递增等待时间
                        logger.warning(f"任务 {task_id} 第 {attempt + 1} 次上传失败，{wait_time}秒后重试: {e}")
                        time.sleep(wait_time)
                    else:
                        logger.error(f"任务 {task_id} 上传失败，已达到最大重试次数: {e}")
            
            if not success:
                results[task_id] = {'success': False, 'error': str(last_error)}
        
        return results
    
    def get_upload_statistics(self) -> Dict[str, Any]:
        """获取上传统计信息"""
        return {
            'success_count': self.upload_success_count,
            'error_count': self.upload_error_count,
            'total_attempts': self.upload_success_count + self.upload_error_count,
            'success_rate': (self.upload_success_count / max(1, self.upload_success_count + self.upload_error_count)) * 100,
            'errors': self.upload_errors,
            'oss_available': self.oss_available
        }


def test_oss_uploader():
    """测试OSS上传器"""
    uploader = OSSUploader()
    
    # 测试模拟数据上传
    test_data = b"test image data"
    result = uploader.upload_image_data(test_data, 'jpg')
    print(f"测试上传结果: {result}")
    
    # 输出统计信息
    stats = uploader.get_upload_statistics()
    print(f"上传统计: {stats}")


if __name__ == "__main__":
    test_oss_uploader()

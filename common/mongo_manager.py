from typing import Any, Dict, List, Optional

from pymongo import MongoClient
from pymongo.collection import Collection
from pymongo.errors import PyMongoError


class MongoDBClient:
    def __init__(self, uri: str, db_name: str):
        try:
            self.client = MongoClient(uri)
            self.db = self.client[db_name]
            print(f"[MongoDB] Connected to {uri}, DB: {db_name}")
        except PyMongoError as e:
            raise ConnectionError(f"Failed to connect to MongoDB: {e}")

    def get_collection(self, collection_name: str) -> Collection:
        return self.db[collection_name]

    def insert_one(self, collection: str, data: Dict) -> str:
        result = self.get_collection(collection).insert_one(data)
        return str(result.inserted_id)

    def insert_many(self, collection: str, data_list: List[Dict]) -> List[str]:
        result = self.get_collection(collection).insert_many(data_list)
        return [str(_id) for _id in result.inserted_ids]

    def find_one(self, collection: str, query: Dict, projection: Optional[Dict] = None) -> Optional[Dict]:
        return self.get_collection(collection).find_one(query, projection)

    def find_many(self, collection: str, query: Dict, projection: Optional[Dict] = None, limit: int = 0,
                  skip: int = 0) -> List[Dict]:
        cursor = self.get_collection(collection).find(query, projection).skip(skip)
        if limit:
            cursor = cursor.limit(limit)
        return list(cursor)

    def update_one(self, collection: str, query: Dict, update: Dict, upsert: bool = False) -> int:
        result = self.get_collection(collection).update_one(query, {"$set": update}, upsert=upsert)
        return result.modified_count

    def update_many(self, collection: str, query: Dict, update: Dict, upsert: bool = False) -> int:
        result = self.get_collection(collection).update_many(query, {"$set": update}, upsert=upsert)
        return result.modified_count

    def delete_one(self, collection: str, query: Dict) -> int:
        result = self.get_collection(collection).delete_one(query)
        return result.deleted_count

    def delete_many(self, collection: str, query: Dict) -> int:
        result = self.get_collection(collection).delete_many(query)
        return result.deleted_count

    def count_documents(self, collection: str, query: Dict) -> int:
        return self.get_collection(collection).count_documents(query)

    def find_with_pagination(self, collection: str, query: Dict, page: int, page_size: int,
                             projection: Optional[Dict] = None, sort: Optional[List] = None) -> Dict[str, Any]:
        skip = (page - 1) * page_size
        cursor = self.get_collection(collection).find(query, projection)

        if sort:
            cursor = cursor.sort(sort)

        total = self.count_documents(collection, query)
        items = list(cursor.skip(skip).limit(page_size))

        return {
            "page": page,
            "page_size": page_size,
            "total": total,
            "items": items
        }

    def drop_collection(self, collection: str):
        self.db.drop_collection(collection)

    def close(self):
        self.client.close()


if __name__ == "__main__":
    mongo = MongoDBClient(
        uri="******************************************",
        db_name="luhao-prod"
    )

    # 插入数据
    _id = mongo.insert_one("users", {"name": "Alice", "age": 30})
    print("Inserted ID:", _id)

    # 查询数据
    user = mongo.find_one("users", {"name": "Alice"})
    print("User:", user)

    # 更新数据
    updated = mongo.update_one("users", {"name": "Alice"}, {"age": 31})
    print("Updated Count:", updated)

    # 分页查询
    result = mongo.find_with_pagination("users", {}, page=1, page_size=5)
    print("Page Result:", result)

    mongo.close()

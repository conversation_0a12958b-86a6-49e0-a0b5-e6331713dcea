import os

# 通过环境变量区分环境
ENV = os.getenv('ENV', 'prod')


# 公共配置
class Config:
    DEBUG_MODEL = 1
    PROJECT_NAME = "nsha"
    LOG_PATH = "static/logs"
    HOST = "http://127.0.0.1:17001"
    PROJECT_PATH = "D:\\nsha"
    ANDROID_PATH = "/storage/emulated/0/Download/"
    ADB_PATH = "adb"
    SCRCPY_PATH = "scrcpy"
    DEVICE_MAX_LOG_SIZE = 30  # 设备日志最大尺寸 单位MB
    DEVICE_MAX_LINE = 100  # 设备日志最多行数
    AUTO_UPLOAD_DATA = True  # 是否上传数据
    AUTO_RETURN_LOGIN = True  # 是否返回登录界面

    server_url = "https://api2.kkzhw.com"
    image_server_url = "https://images2.kkzhw.com/"
    api_upload_images = "/mall-portal/openapi/record/upload_image"
    api_add_image_to_account = "/mall-portal/openapi/record/add_account_images"
    api_get_account = "/mall-portal/openapi/record/get_nshaccount_info"
    app_id = "qd561595395732389"
    secret_key = "8x8coht211zh6l22dci2v7zgzav8zxs5udy23iitt90"

    SERVER_API_TOKEN = "c9q0y6a1m7d9w0h0d8x0m1k0k6c0o2c"

    OSS_ACCESS_KEY_ID = "LTAI5t8j4SZCrnBiFoEzXm7J"
    OSS_ACCESS_KEY_SECRET = "******************************"

    WINDOW_MAX_SIZE = 1280  # 视频大小
    VIDEO_BIT_RATE = "5M"  # 视频比特率
    MAX_FPS = 15  # 最大帧率

    C1_CODE_WAIT_TIME = 180  # 验证码等待时间

    TAP_DELAY = 0.5

    PINGFEN_XIAXIAN = "2200"  # 最小的内功评分
    NEIGONG_ZHUANGBEI_COUNT = 6  # 内功装备数量
    NEIGONG_CLICK_COUNT = 32  # 内功点击的数量，最多一页
    FANYE_BEIBAO = 5  # 背包翻页数量
    FANYE_WUQI = 9  # 武器套装翻页数量
    FANYE_WUQI_HUANSHEN = 4  # 武器环身翻页数量

    MAX_CLOTHES_COUNT = 32  # 外观套装翻页数量
    MAX_FASHIS_COUNT = 32  # 外观发饰翻页数量
    MAX_ZUOQI = 32  # 外观坐骑翻页数量

    CAPTURE_MODE = 1  # 截图模式 0 为快速截图(scrcpy) 1 为慢速高质量截图(screencap)

    SKIP_ATTRI_NAME = "账号专区,最低价格多少,区服,转性CD,转职CD,换绑CD,性别,职业,账号类型,金色武器打造,金色鞋子打造,金色腰带打造,金色衣服护腕打造,首领打造,满级绝技,热门男号自染,热门女号自染,极品内功"

    # 错误替换文本
    ERROR_TEXT = {
        "织星诀": "织星袂",
        "郎": "咩郎",
        "天曦四象·朱雀": "天曦四象朱雀",
        "天曦四象·青龙": "天曦四象青龙",
        "天曦四象·白虎": "天曦四象白虎",
        "千戈玉帛": "干戈玉帛",
        "流光湖": "流光溯",
        "灵倪街威典": "灵倪衔威舆",
        "灵倪衔威典": "灵倪衔威舆",
        "灵貌衔威舆": "灵倪衔威舆",
        "压烟凭虚阀": "蜃烟凭虚阙",
        "幽帘落梦荤": "幽帘落梦辇",
        "幻虹貌": "幻虹猊",
        "幻虹倪": "幻虹猊",
        "幻虹兜": "幻虹猊",
        "冥夜貌": "冥夜猊",
        "点明炉": "点名炉",
        "销干戈": "销千戈",
        "醒狮起": "醒狮",
        "醒狮·起": "醒狮",
        "揽华霄起": "揽华霄",
        "揽华霄·起": "揽华霄",
        "揽华宵·起": "揽华霄",
        "揽华宵：起": "揽华霄",
        "揽华宵:起": "揽华霄",
        "揽华宵起": "揽华霄",
        "御剑起": "御剑",
        "御剑·起": "御剑",
        "御龙起": "御龙",
        "御龙·起": "御龙",
        "墨韵起": "墨韵",
        "墨韵·起": "墨韵",
        "伞梦起": "伞梦",
        "伞梦·起": "伞梦",
        "天马·起": "天马",
        "天马起": "天马",
        "成双·起": "成双",
        "成双起": "成双",
        "千变·起": "千变",
        "千变起": "千变",
        "扶摇·起": "扶摇",
        "扶摇起": "扶摇",
        "幽帘落梦辈": "幽帘落梦辇",
        "瑶帘烟光辈": "瑶帘烟光辇",
        "瑶帘烟光荤": "瑶帘烟光辇",
        "苍雷破障": "苍雷破瘴",
        "蜃海蓬壶阀": "蜃海蓬壶阙",
        "蜃烟凭虚阀": "蜃烟凭虚阙",
        "驰尽凌日": "驰昼凌日",
        "灵鹿桃天驾": "灵鹿桃夭驾",
        "卷红尘": "绻红尘",
        # "瑶台瑾兔":"瑶台瑾兔倚桂",
        "喵巫·魔帽灵": "喵巫魔帽灵",
        "极·驰尽灵槎": "极·驰昼灵槎"
    }
    black_attr_value = {
        "修罗伏魔": "守心镇魔",
        "涅槃无尘": "守心镇魔",
        "刹那幻空": "守心镇魔",
        "须弥万象": "守心镇魔",
        "莲心菩提": "守心镇魔",
        "自在十方": "守心镇魔",
        "无量金刚": "守心镇魔",
        "禅花无界": "守心镇魔",
        "清波震溟": "水武",
        "清波引商": "水武",
        "清波啸空": "水武",
        "清波照梦": "水武",
        "清波绽露": "水武",
        "清波破阵": "水武",
        "清波邀月": "水武",
        "清波化羽": "水武",
        "雪封疆": "冰武",
        "雪阵曲": "冰武",
        "雪凌风": "冰武",
        "雪灵语": "冰武",
        "雪素心": "冰武",
        "雪冰河": "冰武",
        "雪无痕": "冰武",
        "雪凝枢": "冰武",
        "辰火万象": "火武",
        "烽火燎原": "火武",
        "疾火耀日": "火武",
        "绝火独明": "火武",
        "烟火似云": "火武",
        "业火盛花": "火武",
        "天火回风": "火武",
        "烈火燃天": "火武",
        "怒雷摧城": "雷武",
        "闻雷诛邪": "雷武",
        "青雷震岳": "雷武",
        "挽雷掣月": "雷武",
        "惊雷断山": "雷武",
        "苍雷破瘴": "雷武",
        "疾雷游夜": "雷武",
        "幽雷除厄": "雷武"
    }
    chongzhi_chenghao = [
        ("闲云自在", 200),
        ("放歌清宵", 1000),
        ("凭栏听雨", 3000),
        ("把酒临风", 10000),
        ("卧看千山", 50000),
        ("一醉轻王侯", 100000),
        ("秋水不染尘", 200000),
        ("乘风登玉京", 500000),
        ("缥缈云流鹤上仙", 1000000),
        ("150万充值称号", 1500000)
    ]
    lock_keywords = {"任务", "神石", "商城", "其他", "基他", "其它", "基它", "兑换", "副本", "任", "务", "活动"}
    zx_unlock_keywords = {"已", "拥", "有"}
    TSS_VALUE = {
        "玄龙耀世": 0,
        "银河星纱": 0,
        "琼化白羽": 0,
        # "琼华玉凰": 150,
        # "琼华玉凰·极": 240,
        # "铸星幻龙": 150,
        # "铸星幻龙·恒": 240,
        # "寰宇流光": 62,
        # "星河拾梦": 62,
        # "沧溟荡流": 62,
        # "碧澜解语": 62,
        # "冥宙曜火": 62,
        "天枢·斗星移": 1,
        "飞焰流波": 0,
        "混沌": 0,
        "白夜拂雪": 0,
        "星剑分野": 0,
        "星落月皓": 0,
        "紫宿宸极": 0,
        "绝域孤石": 0,
        "冥宙星熠": 62,
        "琼华清露": 1,
        "银河珍丝": 1,
        "琼华白羽": 0,
        "神兵戒指": 0,
        "曦光璞玉": 0,
        "昼宇灵晖": 0,
        "千秋": 1,
        "九霄": 1,
        "雪牵情": 1,
        "朝鹿鸣": 1,
        "白鹿谣": 1,
        "浮云山": 1,
        "灵蔓": 1,
        "霁雪": 1,
        "绛烟": 1,
        "狂歌": 1,
        "泽明": 1,
        "风华": 1,
        "天祈": 1,
        "天钧": 1,
        "龙荒": 1,
        "炽浪": 1,
        "金杯错": 1,
        "挥云": 1,
        "重烟水": 1,
        "紫宸": 1,
        "含曜": 1,
        "翠羽吟": 1,
        "炳耀玉丝": 1,
        "沉壁臻狐": 1,
        "桀骜": 1,
        "琼海": 1,
        "早樱": 1,
        "天刑": 1,
        "熙明": 1,
        "织星袂": 1,
        "昼夜歌": 1,
        "鳞川": 1,
        "碎霄": 1,
        "喵语": 1,
        "幻羽": 1,
        "莹翎": 1,
        "灼妖": 0,
        "璃光": 1,
        "琢雪": 1,
        "红炎": 1,
        "不夜": 1,
        "梦前尘": 1,
        "清宵": 1,
        "炎冥赫赫": 1,
        "青冥曙晖": 1,
        "神陨": 1,
        "遥月": 1,
        "云间墨": 1,
        "织烟": 1,
        "旋悠光": 0,
        "长风烈": 0,
        "狂夜瑰语": 1,
        "无双": 1,
        "寒梦": 1,
        "霜年": 1,
        "锦夜行": 1,
        "絮粉": 1,
        "云绒": 1,
        "绯曜": 1,
        "墨狩": 1,
        "狂欢": 1,
        "飘喵萦梦": 4,
        "喵趣云翔": 4,
        "折星影": 3,
        "灵猊衔威舆": 3,
        "焚天阙": 3,
        "流光溯": 3,
        "蜃海蓬壶阙": 3,
        "玄鹿垂星驾": 3,
        "灵鹿桃夭驾": 3,
        "仙鹿迎曦驾": 3,
        "奔雷动": 3,
        "寒霜凛": 3,
        "夜摧梦": 3,
        "眠鲤宿月亭": 3,
        "策玄戈": 3,
        "蜃烟凭虚阙": 3,
        "夜沉梦": 3,
        "南山雪": 3,
        "玉京仙": 3,
        "幽帘落梦辇": 3,
        "瑶帘烟光辇": 3,
        "贯日猊": 3,
        "幻虹猊": 3,
        "碧月猊": 3,
        "冥夜猊": 3,
        "轻冥幻夜轩": 4,
        "月川灵": 3,
        "青丘空": 3,
        "银汉渺": 3,
        "冰河凌": 3,
        "溯浮生": 3,
        "绻红尘": 3,
        "迷重川": 3,
        "终宵梦": 3,
        "喵巫枫叶橘": 3,
        "喵巫魔帽灵": 3,
        "喵巫解梦语": 3,
        "引华涟": 3,
        "照玉堂": 3,
        "逐暗沸夜": 3,
        "驰昼凌日": 3,
        "彼岸芳华": 3,
        "彼岸赤华": 3,
        "喵巫祐夜眠": 3,
        "极·逐暗天梭": 3,
        "极·驰昼灵槎": 3,
        "瑶台瑾兔倚桂": 3,
        "瑶台瑾兔灼桃": 3,
        "明河曙天·醉卧西海": 1,
        "山海祠神·赤凰重明": 1,
        "重蝶化梦": 1,
        "日曜八荒·轩辕剑": 1,
        "青丘雪": 1,
        "岁星行渡·千重焰": 1,
        "明河曙天·长鲸天斗": 1,
        "山海祠神·阳升羲和": 1,
        "天曦四象·白虎": 1,
        "天曦四象·朱雀": 1,
        "天曦四象白虎": 1,
        "天曦四象朱雀": 1,
        "天曦四象·青龙": 1,
        "灵宠·心月狐": 1,
        "天狼星": 1,
        "降魔破邪·金刚明王": 1,
        "天鹰座": 1,
        "新世华霓·喵到病除": 1,
        "妖影寻灵·狐舞青丘": 1,
        "丹青纵横·龙潭墨云": 1,
        "百鬼问道·幽冥狼影": 1,
        "修罗伏魔": 0,
        "涅槃无尘": 0,
        "刹那幻空": 0,
        "须弥万象": 0,
        "莲心菩提": 0,
        "自在十方": 0,
        "无量金刚": 0,
        "禅花无界": 0,
        "守心镇魔": 0,
        "辰火万象": 1,
        "烽火燎原": 1,
        "疾火耀日": 1,
        "绝火独明": 1,
        "烟火似云": 1,
        "业火盛花": 1,
        "天火回风": 1,
        "烈火燃天": 1,
        "火武": 1,
        "雪封疆": 1,
        "雪阵曲": 1,
        "雪凌风": 1,
        "雪灵语": 1,
        "雪素心": 1,
        "雪冰河": 1,
        "雪无痕": 1,
        "雪凝枢": 1,
        "冰武": 1,
        "怒雷摧城": 1,
        "闻雷诛邪": 1,
        "青雷震岳": 1,
        "挽雷掣月": 1,
        "惊雷断山": 1,
        "苍雷破瘴": 1,
        "疾雷游夜": 1,
        "幽雷除厄": 1,
        "雷武": 1,
        "清波震溟": 1,
        "清波引商": 1,
        "清波啸空": 1,
        "清波照梦": 1,
        "清波绽露": 1,
        "清波破阵": 1,
        "清波邀月": 1,
        "清波化羽": 1,
        "水武": 1,
        "蜃海尘踪": 1,
        "墨法天地": 1,
        "星朔九天": 1,
        "尘缘引梦": 1,
        "疾鹰掠野": 1,
        "幻枢归奇": 1,
        "判道狱火": 1,
        "竹龙彻虹": 1,
        "蜻影飞光": 1,
        "星喵梦使": 1,
        "伏魔修罗": 0,
        "鹤舞云仙": 0,
        "御龙": 0,
        "揽华宵": 1,
        "揽华霄": 1,
        "御剑": 1,
        "墨韵": 0,
        "伞梦": 0,
        "扶摇": 0,
        "醒狮": 0,
        "锦羽寒光": 0,
        "千变": 1
    }

    create_product_req = {
        "gameCareinfoPhone2": "",
        "pushType": 1,
        "gameAccountQufu": "",
        "productAttributeValueList": [
            {
                "productAttributeId": 114,
                "value": "",
                "attriName": "职业",
                "type": 1,
                "searchType": 1
            },
            {
                "productAttributeId": 108,
                "value": "",
                "attriName": "账号类型",
                "type": 1,
                "searchType": 1
            },
            {
                "productAttributeId": 105,
                "value": "",
                "attriName": "性别",
                "type": 1,
                "searchType": 1
            },
            {
                "productAttributeId": 852,
                "value": "",
                "attriName": "换绑CD",
                "type": 1,
                "searchType": 1
            },
            {
                "productAttributeId": 107,
                "value": "",
                "attriName": "转职CD",
                "type": 1,
                "searchType": 1
            },
            {
                "productAttributeId": 106,
                "value": "",
                "attriName": "转性CD",
                "type": 1,
                "searchType": 1
            },
            {
                "productAttributeId": 872,
                "value": "",
                "attriName": "角色等级",
                "type": 1,
                "searchType": 0
            },
            {
                "productAttributeId": 96,
                "value": "",
                "attriName": "评分",
                "type": 1,
                "searchType": 2
            },
            {
                "productAttributeId": 97,
                "value": "",
                "attriName": "衣品",
                "type": 1,
                "searchType": 2
            },
            {
                "productAttributeId": 119,
                "value": "",
                "attriName": "国色值",
                "type": 1,
                "searchType": 2
            },
            {
                "productAttributeId": 118,
                "value": "",
                "attriName": "已使用天赏石",
                "type": 1,
                "searchType": 2
            },
            {
                "productAttributeId": 873,
                "value": "",
                "attriName": "未使用天赏石",
                "type": 1,
                "searchType": 2
            },
            {
                "productAttributeId": 1185,
                "value": "",
                "attriName": "限定纹玉额度",
                "type": 1,
                "searchType": 2
            },
            {
                "productAttributeId": 371,
                "value": "",
                "attriName": "灵韵数量",
                "type": 1,
                "searchType": 2
            },
            {
                "productAttributeId": 166,
                "value": "",
                "attriName": "天霓染",
                "type": 1,
                "searchType": 2
            },
            {
                "productAttributeId": 374,
                "value": "",
                "attriName": "充值金额",
                "type": 1,
                "searchType": 2
            },
            {
                "productAttributeId": 344,
                "value": "",
                "attriName": "稀有外观",
                "type": 2,
                "searchType": 3
            },
            {
                "productAttributeId": 899,
                "value": "",
                "attriName": "稀有道具",
                "type": 2,
                "searchType": 3
            },
            {
                "productAttributeId": 115,
                "value": "",
                "attriName": "天赏发型",
                "type": 2,
                "searchType": 3
            },
            {
                "productAttributeId": 116,
                "value": "",
                "attriName": "天赏祥瑞",
                "type": 2,
                "searchType": 3
            },
            {
                "productAttributeId": 372,
                "value": "",
                "attriName": "天赏技能皮肤",
                "type": 2,
                "searchType": 3
            },
            {
                "productAttributeId": 554,
                "value": "",
                "attriName": "其他天赏道具",
                "type": 2,
                "searchType": 3
            },
            {
                "productAttributeId": 1071,
                "value": "",
                "attriName": "天赏宠物",
                "type": 2,
                "searchType": 3
            },
            {
                "productAttributeId": 902,
                "value": "",
                "attriName": "灵韵内功",
                "type": 2,
                "searchType": 3
            },
            {
                "productAttributeId": 1072,
                "value": "",
                "attriName": "满级群侠",
                "type": 2,
                "searchType": 3
            },
            {
                "productAttributeId": 1073,
                "value": "",
                "attriName": "满级绝技",
                "type": 2,
                "searchType": 3
            },
            {
                "productAttributeId": 1074,
                "value": "",
                "attriName": "首领打造",
                "type": 2,
                "searchType": 3
            },
            {
                "productAttributeId": 545,
                "value": "",
                "attriName": "充值称号",
                "type": 2,
                "searchType": 3
            },
            {
                "productAttributeId": 373,
                "value": "",
                "attriName": "武器外观",
                "type": 2,
                "searchType": 3
            },
            {
                "productAttributeId": 546,
                "value": "",
                "attriName": "绝版外观",
                "type": 2,
                "searchType": 3
            },
            {
                "productAttributeId": 553,
                "value": "",
                "attriName": "商城外观",
                "type": 2,
                "searchType": 3
            },
            {
                "productAttributeId": 550,
                "value": "",
                "attriName": "轻功外观",
                "type": 2,
                "searchType": 3
            },
            {
                "productAttributeId": 552,
                "value": "",
                "attriName": "金色武器打造",
                "type": 2,
                "searchType": 3
            },
            {
                "productAttributeId": 547,
                "value": "",
                "attriName": "金色项链打造",
                "type": 2,
                "searchType": 3
            },
            {
                "productAttributeId": 548,
                "value": "",
                "attriName": "金色鞋子打造",
                "type": 2,
                "searchType": 3
            },
            {
                "productAttributeId": 549,
                "value": "",
                "attriName": "金色衣服护腕打造",
                "type": 2,
                "searchType": 3
            },
            {
                "productAttributeId": 1162,
                "value": "",
                "attriName": "金色腰带打造",
                "type": 2,
                "searchType": 3
            },
            {
                "productAttributeId": 1180,
                "value": "",
                "attriName": "热门男号自染",
                "type": 2,
                "searchType": 3
            },
            {
                "productAttributeId": 1181,
                "value": "",
                "attriName": "热门女号自染",
                "type": 2,
                "searchType": 1
            },
            {
                "productAttributeId": 333,
                "value": "",
                "attriName": "游戏账号",
                "type": 5,
                "searchType": 0
            },
            {
                "productAttributeId": 334,
                "value": "",
                "attriName": "游戏密码",
                "type": 5,
                "searchType": 0
            },
            {
                "productAttributeId": 335,
                "value": "",
                "attriName": "确认密码",
                "type": 5,
                "searchType": 0
            },
            {
                "productAttributeId": 336,
                "value": "",
                "attriName": "账号来源",
                "type": 5,
                "searchType": 0
            },
            {
                "productAttributeId": 113,
                "value": "",
                "attriName": "区服",
                "type": 1,
                "searchType": 1
            }
        ],
        "pic": "",
        "albumPics": "",
        "description": "",
        "gameCareinfoPhone": "",
        "gameCareinfoVx": "",
        "gameCareinfoTime": "00-23",
        "price": "",
        "originalPrice": "",
        "productCategoryId": "75",
        "productCategoryName": "逆水寒手游",
        "productAttributeCategoryId": "17",
        "gameGoodsSaletype": 0,
        "gameGoodsYijia": 1
    }

    wz_req_config = {
        # 基础认证
        'token': '3he9kqHH',
        'user_id': '99827515',

        # 加密参数
        'encodeParam': 'gACd5iWvGgu68QL5kHhcvrckqyHY/3U1/+p6ppJ92KVeSQ4/Cg7oqHHIt5v6FmQ2/CRDkd0rX7SRc0guhPmmpRGIlHjmKV4haRbBFl+01IeZ9fj2IMKPRaOOY59ILPbNNXrPbw==',

        # 游戏角色信息
        'gameareaid': '2',
        'gameroleid': '2378790952',
        'gameserverid': '2030',
        'gameopenid': '8A7219BF30D9B63635D23D21A1531D81',

        # 用户属性
        'kohdimgender': '2',
        'gameusersex': '2',
        'openid': '6D4FE381F12DA574EB8B14D5EBBE0BCD',

        'default': {  # N0 diQQ
            'token': '69g4eCC2',
            'user_id': '1606537036',
            'encodeParam': 'VSUtTUy4ThK8IzlKOgji+pSXYj1zhgsen0AD0IzMSRM+ER3WtUxyH1H7+y/jqCXyCd54FxN7VJARC6a89v5h38Pe95F5TjoSnHLXonV2T0sHerzprgLFhuo+KbhqzSvYpo3hcA==',
            'gameareaid': '0',
            'gameroleid': '0',
            'gameserverid': '0',
            'gameopenid': '',
            'kohdimgender': '2',
            'gameusersex': '1',
            'openid': '9481E26875C1D7C4687655AB2199A356',
        },
        '51': {  # N1 diWX
            'token': 'DDhZNzR3',
            'user_id': '1681528269',
            'encodeParam': 'aGwAgWQZyND3zjBYZVFs/mPvakwq3zDPh8e77DP6F3/Z2VhM3AABvcl3H8bHlHVB3NGziCM42cXc8Z/OMfc2J2HaG3TVDzG+qX1BWVjCQ5J8Jmtjj8i2drlB1umJA77R0wAMyg==',
            'gameareaid': '3',
            'gameroleid': '4061498528',
            'gameserverid': '3395',
            'gameopenid': 'owanlstjyIrWYH7_-hn1dKph2Ilg',
            'kohdimgender': '2',
            'gameusersex': '1',
            'openid': 'oFhrws0FWKOB0WWYECC4M77n2Xpg',
        },
        '52-登录失效': {  # N2 muQQ
            'token': '3he9kqHH',
            'user_id': '99827515',
            'encodeParam': 'gACd5iWvGgu68QL5kHhcvrckqyHY/3U1/+p6ppJ92KVeSQ4/Cg7oqHHIt5v6FmQ2/CRDkd0rX7SRc0guhPmmpRGIlHjmKV4haRbBFl+01IeZ9fj2IMKPRaOOY59ILPbNNXrPbw==',
            'gameareaid': '2',
            'gameroleid': '2378790952',
            'gameserverid': '2030',
            'gameopenid': '8A7219BF30D9B63635D23D21A1531D81',
            'kohdimgender': '2',
            'gameusersex': '2',
            'openid': '6D4FE381F12DA574EB8B14D5EBBE0BCD',
        },
        '53': {  # N3 JLQQ
            'token': 'srxF9qSs',
            'user_id': '338130766',
            'encodeParam': 'MLtYW7MPZ8JONJUzKNExtx8fHSBbRziJ4SMI5SoUGKl0foqr1kty0PAC7/Ryka9xqVk0WRc4mDcTsms49hypgdhN3NRNUwX+pNTWQFNR4hiCbH7ZAVtHjx0OtYJGoUx8yKyAQg==',
            'gameareaid': '1',
            'gameroleid': '267120899',
            'gameserverid': '1016',
            'gameopenid': '928BFA1C15006C6846DB0524F7D81718',
            'kohdimgender': '2',
            'gameusersex': '1',
            'openid': 'FA534D84F09B4A6DBA1BC32DBBAAEAF4',
        },
        '54': {  # N4 JLWX
            'token': '52Y5XgVj',
            'user_id': '416132623',
            'encodeParam': 'UTflR+1wVh4KTDBTWX1XfaEkkKwR+ES5Wp13nsxtbujMtay9EXUSjusOl+/OXRIkhbMdHcEQW0VSBxgDOlh4jIPKhCSo521xxyrpW4sQnOgXewy9cDh57fZgChX+/UBZysZ3AQ==',
            'gameareaid': '4',
            'gameroleid': '2038580095',
            'gameserverid': '4084',
            'gameopenid': 'owanlsuGS7e9TjBgEwdI7CeUSxWk',
            'kohdimgender': '2',
            'gameusersex': '1',
            'openid': 'oFhrwsz2cMMkTqLERUm6NLp7FoUo',
        },
        '55': {  # N5
            'token': 'WHuZBFqP',
            'user_id': '95294746',
            'encodeParam': 'bws/eDZzo6WBJYvnoFHDXVaHIMu+U1Yjksnin1Y/1/JlqrxGHK6roN6gwe3fMQJM0LylKMWFsjGza5quGM8t8aRa/clW4CdOv4nC0rouQN6DftvyD6XmHQIDrNbhr796rXxBVQ==',
            'gameareaid': '2',
            'gameroleid': '131462511',
            'gameserverid': '2056',
            'gameopenid': '2F4736E45D2DFCB94BF642D4EFC37791',
            'kohdimgender': '2',
            'gameusersex': '1',
            'openid': '1E633FD30528B1FE09A453EE4613E901',
        },
        '56': {  # N6
            'token': 'vwdVeBBh',
            'user_id': '56539010',
            'encodeParam': 'SB/By9PEGGR0OMvWG24YD4LZg8PdLOonzvx57sUMDNiFWN7+GnQUOJpoFHvkDW2L7OGmnu1eR8PoyfmDpQjr+DZfxDkjPE0WyF0H3pJtyZHlij6B+YJaiQh9EiI0g9Ft8/IRgQ==',
            'gameareaid': '1',
            'gameroleid': '2245049385',
            'gameserverid': '1322',
            'gameopenid': '660F3A4A1D91909A6DE7485C73B9E5B5',
            'kohdimgender': '2',
            'gameusersex': '1',
            'openid': 'D65CD9277AD21054D5B1E2C4C32D6545',
        },
        # '57': {  # N7 离线
        #     'token': 'sbd32zsU',
        #     'user_id': '1735163094',
        #     'encodeParam': 'iEY7o9jxntkRfqY6GOcJOcG3cj1i4XvVRObEQzvyRmmjDdTOsFhM7rIm+GfkJTOWeHrpmzUkWm5LSgMDQtiudlhFYWGlavMl4sB76irQIpglw3rq+TzmoQhNFQonCYPe0FiRVg==',
        #     'gameareaid': '3',
        #     'gameroleid': '2478255468',
        #     'gameserverid': '3078',
        #     'gameopenid': 'owanlsvMhH5izkPHYcHQei5VsAms',
        #     'kohdimgender': '2',
        #     'gameusersex': '1',
        #     'openid': 'oFhrws2RaaN6ZCBmxXLMUsODHPbI',
        # },
        '57': {  # N12 diQQ2
            'token': '9ucUMKrJ',
            'user_id': '1633628716',
            'encodeParam': 'j/+fLLAe/7aHCMMEiW3WzY+/v1Injx+RjwNZY0Pck7d4/uhV9arP9O7m244ov+CpgVVYy7qVJU9fifUWiKZFpbBbwYPW5sYiv6Cm634bovmOa1zweY0VafskpbyYQTR5rJdR2A==',
            'gameareaid': '0',
            'gameroleid': '0',
            'gameserverid': '0',
            'gameopenid': 'owanlsuGS7e9TjBgEwdI7CeUSxWk',
            'kohdimgender': '2',
            'gameusersex': '1',
            'openid': 'EECAD0579B3BA58C3C3689570F6382E6',
        },
        '58': {  # N8 JL
            'token': 'eVMgkPPM',
            'user_id': '459671900',
            'encodeParam': 'i+77vlvHyojEn9VrCH7gMhJOWC03PTvxCuXbP/vV6w6xt1vARijmbmNvIOGL7u9D7O8z3Wy0dIvH5ZeYcsyincueFfOve2zEdPD+nZULOJrn+sSOv7Lt4mqywoNGLt2+3ZJa+Q==',
            'gameareaid': '0',
            'gameroleid': '0',
            'gameserverid': '0',
            'gameopenid': 'owanlsuGS7e9TjBgEwdI7CeUSxWk',
            'kohdimgender': '2',
            'gameusersex': '1',
            'openid': '51CC191DCEFE031B5B42FFF01221979A',
        },
        '59': {  # N9
            'token': 'jawva9V7',
            'user_id': '470597618',
            'encodeParam': '0/vVP4YcHckOKqXkoHoc63vgxX0lXpyqOKHRTVv/Az5Xa8sPEJd4vq0c5k5lOwgs2nN7fRaxvMnqFcT7tDJvyxYugzBOF4/9F/v1z/JDk3KPqFOoScEfAYotphTsUueSmVd0QQ==',
            'gameareaid': '4',
            'gameroleid': '1300273224',
            'gameserverid': '4060',
            'gameopenid': 'owanlsuP_097GaeWDlELKJQwgJUQ',
            'kohdimgender': '2',
            'gameusersex': '1',
            'openid': 'oFhrwszbTdleJfigoIuHMirjJ76Q',
        },
        '60': {  # N10
            'token': 'MFtjB9Ye',
            'user_id': '344581281',
            'encodeParam': 'Zr+On/zIA12TIOvE4HICt8iWRF1l4SAf1m5WfQ4yfN9o1dUep3FuN+1HKetN+MgzJHwp9SJaOGpCwyzpUkCTOa0S5s0A5PBCT9ulqR5mfEgh3ByFtVdcTra4OKUBuDRkRT9uTg==',
            'gameareaid': '1',
            'gameroleid': '177954740',
            'gameserverid': '1013',
            'gameopenid': '4D04294E0C00E54B607EECA5AB88426E',
            'kohdimgender': '2',
            'gameusersex': '1',
            'openid': 'FD579B6C1990E58045F632DAF18E70F9',
        },
        '61': {  # N11
            'token': 'Vk9eMPD4',
            'user_id': '1601642832',
            'encodeParam': 'CNXzxqbXY1EvMM9ttfWTK+xOF/+aASlBRkcMDMDtrdgXDFz08R3tYEs7CwKfZoTQ4sgZ4CQFBc34FL5YJG8Xke32T1Blag5EJXiTeW4nHiBvX1CIp4xIcyGUrArVQ8+II4m5Hg==',
            'gameareaid': '0',
            'gameroleid': '0',
            'gameserverid': '0',
            'gameopenid': 'owanlsuGS7e9TjBgEwdI7CeUSxWk',
            'kohdimgender': '2',
            'gameusersex': '1',
            'openid': '725F0CABE78506A297788FE3117B2B2E',
        },
        '62': {  # N12 diQQ2
            'token': '9ucUMKrJ',
            'user_id': '1633628716',
            'encodeParam': 'j/+fLLAe/7aHCMMEiW3WzY+/v1Injx+RjwNZY0Pck7d4/uhV9arP9O7m244ov+CpgVVYy7qVJU9fifUWiKZFpbBbwYPW5sYiv6Cm634bovmOa1zweY0VafskpbyYQTR5rJdR2A==',
            'gameareaid': '0',
            'gameroleid': '0',
            'gameserverid': '0',
            'gameopenid': 'owanlsuGS7e9TjBgEwdI7CeUSxWk',
            'kohdimgender': '2',
            'gameusersex': '1',
            'openid': 'EECAD0579B3BA58C3C3689570F6382E6',
        },
        '52': {  # N13 diWX2
            'token': 'RtWKv9Jk',
            'user_id': '1609449140',
            'encodeParam': '45GgkbKr9nWmr782zN8mYPv5usyqzkcKT9ojZveDU+fNgc7vWDNCDUMfFOZHXB9sb0nFea9wUhBSFZuCkMi8Uyqdk0zbTseY14ChhnBiTbl1eH1CLLDXDm+cMOdqcaxUXxV/Rw==',
            'gameareaid': '0',
            'gameroleid': '0',
            'gameserverid': '0',
            'gameopenid': 'owanlsuGS7e9TjBgEwdI7CeUSxWk',
            'kohdimgender': '2',
            'gameusersex': '1',
            'openid': 'oFhrws8kjqbrXWEMtUioUwkJl2cE',
        },
    }


# 开发环境配置
class DevConfig(Config):
    DEBUG = True
    server_url = "http://192.168.3.101:8201"
    retry_device_list = [39]


# 测试环境配置
class TestConfig(Config):
    server_url = "https://api.kkzhw.com"
    retry_device_list = [39]


# 生产环境配置
class ProdConfig(Config):
    # server_url = "http://api2.kkzhw.com"
    server_url = "http://api.yyd8.com"
    retry_device_list = [58]


# 根据环境选择配置
config_by_env = {
    'dev': DevConfig,
    'test': TestConfig,
    'prod': ProdConfig,
}

# 当前配置
current_config = config_by_env[ENV]
print('#当前环境配置,', ENV)
print(current_config.server_url)

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel图片提取器
功能：从Excel文件中提取嵌入的图片
"""

import os
import io
import logging
from typing import Dict, List, Optional, Tuple
from zipfile import ZipFile
import xml.etree.ElementTree as ET
from PIL import Image

logger = logging.getLogger(__name__)


class ExcelImageExtractor:
    """Excel图片提取器"""
    
    def __init__(self, excel_file: str):
        self.excel_file = excel_file
        self.extracted_images = {}
        self.image_positions = {}
    
    def extract_images_from_xlsx(self) -> Dict[str, bytes]:
        """从XLSX文件中提取所有图片"""
        try:
            logger.info(f"开始从Excel文件提取图片: {self.excel_file}")
            
            # XLSX文件实际上是一个ZIP压缩包
            with ZipFile(self.excel_file, 'r') as zip_file:
                # 获取所有文件列表
                file_list = zip_file.namelist()
                
                # 查找图片文件（可能在xl/media/或xl/drawings/media/目录下）
                image_files = [f for f in file_list if
                              (f.startswith('xl/media/') or f.startswith('xl/drawings/media/')) and
                              f.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.bmp'))]
                
                logger.info(f"找到 {len(image_files)} 个图片文件")
                
                images = {}
                for img_file in image_files:
                    try:
                        # 读取图片数据
                        image_data = zip_file.read(img_file)
                        
                        # 生成图片ID（使用文件名）
                        img_name = os.path.basename(img_file)
                        images[img_name] = image_data
                        
                        logger.info(f"提取图片: {img_name} ({len(image_data)} bytes)")
                        
                    except Exception as e:
                        logger.error(f"提取图片 {img_file} 时出错: {e}")
                
                # 尝试解析图片位置信息
                self._parse_image_positions(zip_file)

                # 保存提取的图片到实例变量
                self.extracted_images = images

                return images
                
        except Exception as e:
            logger.error(f"提取Excel图片时出错: {e}")
            return {}
    
    def _parse_image_positions(self, zip_file: ZipFile):
        """解析图片在工作表中的位置"""
        try:
            # 查找绘图关系文件
            drawing_files = [f for f in zip_file.namelist() if 'drawings/drawing' in f and f.endswith('.xml')]

            for drawing_file in drawing_files:
                try:
                    # 读取绘图XML
                    drawing_xml = zip_file.read(drawing_file).decode('utf-8')
                    root = ET.fromstring(drawing_xml)

                    # 定义命名空间
                    namespaces = {
                        'xdr': 'http://schemas.openxmlformats.org/drawingml/2006/spreadsheetDrawing',
                        'a': 'http://schemas.openxmlformats.org/drawingml/2006/main',
                        'r': 'http://schemas.openxmlformats.org/officeDocument/2006/relationships'
                    }

                    # 查找所有图片锚点
                    anchors = root.findall('.//xdr:oneCellAnchor', namespaces)

                    for i, anchor in enumerate(anchors):
                        from_elem = anchor.find('xdr:from', namespaces)
                        if from_elem is not None:
                            col = from_elem.find('xdr:col', namespaces)
                            row = from_elem.find('xdr:row', namespaces)

                            if col is not None and row is not None:
                                col_num = int(col.text)
                                row_num = int(row.text)

                                # 存储位置信息
                                image_key = f"image{i+1}.png"  # 假设图片按顺序命名

                                # 根据绘图文件推断工作表索引
                                # drawing1.xml 对应 sheet1 (第一个工作表)
                                sheet_index = int(drawing_file.split('drawing')[1].split('.xml')[0]) - 1

                                self.image_positions[image_key] = {
                                    'col': col_num,
                                    'row': row_num,
                                    'sheet_index': sheet_index,  # 工作表索引 (0-based)
                                    'drawing_file': drawing_file
                                }

                                logger.info(f"图片 {image_key} 位置: 列{col_num}, 行{row_num}")

                except Exception as e:
                    logger.warning(f"解析绘图文件 {drawing_file} 时出错: {e}")

        except Exception as e:
            logger.warning(f"解析图片位置时出错: {e}")

    def get_image_for_position(self, sheet_name: str, row_index: int) -> Optional[bytes]:
        """根据工作表和行索引获取对应的图片"""
        try:
            # 查找匹配的图片
            for img_name, position in self.image_positions.items():
                # Excel行号转换为pandas行索引
                # Excel行号从0开始，但通常第0行是标题行
                # 所以Excel行1对应pandas行0，Excel行2对应pandas行1，以此类推
                excel_row = position['row']
                pandas_row_index = excel_row - 1  # 考虑标题行偏移

                logger.debug(f"图片 {img_name}: Excel行{excel_row} -> pandas行{pandas_row_index}, 目标行{row_index}")

                # 检查行是否匹配
                if pandas_row_index == row_index:
                    if img_name in self.extracted_images:
                        logger.info(f"为 {sheet_name} 第{row_index+1}行找到匹配图片: {img_name} (Excel行{excel_row})")
                        return self.extracted_images[img_name]

            return None

        except Exception as e:
            logger.error(f"获取位置图片时出错: {e}")
            return None
    
    def save_extracted_images(self, output_dir: str = "extracted_images") -> Dict[str, str]:
        """保存提取的图片到本地文件"""
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        images = self.extract_images_from_xlsx()
        saved_files = {}
        
        for img_name, img_data in images.items():
            try:
                # 保存图片文件
                file_path = os.path.join(output_dir, img_name)
                with open(file_path, 'wb') as f:
                    f.write(img_data)
                
                saved_files[img_name] = file_path
                logger.info(f"图片已保存: {file_path}")
                
            except Exception as e:
                logger.error(f"保存图片 {img_name} 时出错: {e}")
        
        return saved_files
    
    def get_image_info(self) -> List[Dict]:
        """获取图片信息"""
        images = self.extract_images_from_xlsx()
        image_info = []

        print(f"提取到 {len(images)} 个图片")  # 调试信息

        for img_name, img_data in images.items():
            try:
                print(f"处理图片: {img_name}, 大小: {len(img_data)} bytes")  # 调试信息

                # 使用PIL获取图片信息
                with Image.open(io.BytesIO(img_data)) as img:
                    info = {
                        'name': img_name,
                        'size': len(img_data),
                        'format': img.format,
                        'dimensions': img.size,
                        'mode': img.mode
                    }
                    image_info.append(info)
                    print(f"图片信息: {info}")  # 调试信息

            except Exception as e:
                print(f"获取图片信息 {img_name} 时出错: {e}")  # 调试信息
                logger.error(f"获取图片信息 {img_name} 时出错: {e}")

        return image_info


def analyze_excel_structure(excel_file: str):
    """分析Excel文件结构，查看是否包含图片"""
    try:
        print(f"分析Excel文件: {excel_file}")
        
        with ZipFile(excel_file, 'r') as zip_file:
            file_list = zip_file.namelist()
            
            print(f"文件总数: {len(file_list)}")
            print("\n主要目录结构:")
            
            directories = set()
            for file_path in file_list:
                if '/' in file_path:
                    dir_name = file_path.split('/')[0]
                    directories.add(dir_name)
            
            for directory in sorted(directories):
                files_in_dir = [f for f in file_list if f.startswith(directory + '/')]
                print(f"  {directory}/ ({len(files_in_dir)} 文件)")
            
            # 查找图片文件
            image_files = [f for f in file_list if f.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.bmp'))]
            print(f"\n找到的图片文件 ({len(image_files)}):")
            for img_file in image_files:
                print(f"  {img_file}")
            
            # 查找绘图相关文件
            drawing_files = [f for f in file_list if 'drawing' in f.lower()]
            print(f"\n绘图相关文件 ({len(drawing_files)}):")
            for drawing_file in drawing_files:
                print(f"  {drawing_file}")
                
    except Exception as e:
        print(f"分析Excel文件时出错: {e}")


def test_image_extraction():
    """测试图片提取功能"""
    excel_file = "游戏知识问答测试题库.xlsx"
    
    if not os.path.exists(excel_file):
        print(f"Excel文件不存在: {excel_file}")
        return
    
    # 分析文件结构
    analyze_excel_structure(excel_file)
    
    # 提取图片
    extractor = ExcelImageExtractor(excel_file)
    
    # 获取图片信息
    image_info = extractor.get_image_info()
    print(f"\n图片信息:")
    for info in image_info:
        print(f"  {info['name']}: {info['format']}, {info['dimensions']}, {info['size']} bytes")
    
    # 保存图片到本地
    if image_info:
        saved_files = extractor.save_extracted_images()
        print(f"\n已保存 {len(saved_files)} 个图片文件")
        for img_name, file_path in saved_files.items():
            print(f"  {img_name} -> {file_path}")
    else:
        print("\n未找到图片文件")


if __name__ == "__main__":
    test_image_extraction()

from enum import Enum


class TaskEvent:
    def __init__(self, task_id, stage, status, snapshot=None, data=None, msg=None, product_id=None):
        self.task_id = task_id  # 任务ID
        self.stage = stage
        self.status = status
        self.snapshot = snapshot
        self.data = data
        self.msg = msg
        self.product_id = product_id

    def __repr__(self):
        return f"TaskEvent(task_id={self.task_id}, stage={self.stage}, status={self.status}, data={self.data}, msg={self.msg})"


# 事件状态常量
class EventStatus(Enum):
    SUCCESS = "成功"
    FAILURE = "失败"
    MANUAL_REQUIRED = "需人工处理"
    IN_PROGRESS = "进行中"
    TIMEOUT = "超时"
    FINISHED = "已完成"
    CANCELLED = "已取消"

    ERROR = "错误"
    RETRYING = "重试中"


# print(EventStatus.FINISHED)

class StepManager:
    def __init__(self, total_steps=12):
        self.total_steps = total_steps
        self.steps = {
            'init_local_nsh': '初始化',
            'safe_step_juese': '角色/仓库',
            'safe_step_jueji': '绝技',
            'safe_step_neigong': '内功',
            'safe_step_dazao': '打造',
            'safe_step_waiguan': '外观',
            'safe_step_kaifang_shijie': '开放世界',
            'safe_step_qunxia': '群侠',
            'safe_step_zhuangyuan': '庄园',
            'safe_step_lingcong': '灵宠',
            'step_img_upload': '图片上传',
            'step_meta_upload': '元数据上传',
        }

    def get_step_info(self, key):
        """根据给定的 key 返回中文名和进度"""
        if key in self.steps:
            name = self.steps[key]
            index = list(self.steps.keys()).index(key) + 1  # 获取序号
            progress = f'{index}/{self.total_steps}'
            return name, progress
        return None, None


class FuncResponse:
    """统一函数响应封装类
    """

    def __init__(self, code: int = -1, data=None, msg: str = None):
        """
        Args:
            code: 状态码（0=成功，非0=失败）
            data: 返回数据
            msg: 错误描述（失败时必需）
        """
        self.code = code
        self.data = data
        self.msg = msg
        self.success = code == 0

    def to_dict(self) -> dict:
        """转换为标准字典格式"""
        return {
            'code': self.code,
            'data': self.data,
            'msg': self.msg,
        }

    @classmethod
    def ok(cls, data=None, code: int = 0) -> 'FuncResponse':
        """创建成功响应"""
        return cls(code=code, data=data)

    @classmethod
    def fail(cls, msg: str) -> 'FuncResponse':
        """创建失败响应
        code = -1 表示失败
        code = 1 需要重试
        """
        return cls(code=-1, msg=msg)

    @classmethod
    def fail_need_retry(cls, msg: str = '服务器繁忙') -> 'FuncResponse':
        """创建失败响应
        code = -1 表示失败
        code = 1 需要重试
        """
        return cls(code=1, msg=msg)

    def need_retry(self) -> bool:
        """检查是否需要重试"""
        return self.code == 1

    def is_ok(self) -> bool:
        """检查是否成功"""
        return self.success

    def get_data_or_raise(self) -> any:
        """获取数据（失败时抛出异常）"""
        if not self.success:
            raise ValueError(self.msg or "Operation failed")
        return self.data


# 示例用法
if __name__ == "__main__":
    step_manager = StepManager()

    key = 'safe_step_lingcong'
    info = step_manager.get_step_info(key)

    if info:
        name, progress = info
        print(f'中文名: {name}, 进度: {progress}')
    else:
        print('未找到该步骤的信息。')

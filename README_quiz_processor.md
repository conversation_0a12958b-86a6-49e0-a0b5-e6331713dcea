# 游戏知识问答题库数据处理器

## 项目概述

本项目用于处理游戏知识问答题库的Excel数据，将其转换为可直接导入数据库的SQL语句。支持问答题、选择题、判断题三种题型，并具备图片处理和OSS上传功能。

## 功能特性

- ✅ **多题型支持**：问答题、选择题、判断题
- ✅ **智能答案识别**：自动从选项和答案解析中提取正确答案
- ✅ **图片处理**：支持图片压缩、格式转换和OSS上传
- ✅ **数据验证**：完整的数据清洗和验证机制
- ✅ **错误处理**：详细的错误日志和处理统计
- ✅ **SQL生成**：生成标准的INSERT语句，可直接执行

## 文件结构

```
├── quiz_data_processor.py      # 主处理器
├── image_processor.py          # 图片处理模块
├── oss_integration.py          # OSS上传集成
├── quiz_questions_insert.sql   # 生成的SQL文件
├── quiz_processor.log          # 处理日志
└── quiz_processor_errors.json  # 错误记录
```

## 数据库表结构

```sql
create table quiz_questions
(
    id              bigint auto_increment comment '主键ID' primary key,
    game_category   varchar(100)                          not null comment '游戏分类',
    question_type   varchar(100)                          not null comment '题目类型：判断题；选择题；问答题',
    difficulty      varchar(20) default '中等'            not null comment '题目难度：简单、中等、困难',
    content         varchar(500)                          not null comment '题目内容',
    image_url       varchar(500)                          null comment '题目配图URL',
    options         json                                  null comment '选择题选项（JSON格式）',
    correct_answer  text                                  not null comment '正确答案',
    answer_analysis text                                  null comment '答案解析',
    status          tinyint(1)  default 1                 null comment '状态：0->禁用；1->启用',
    create_time     datetime    default CURRENT_TIMESTAMP null comment '创建时间',
    update_time     datetime    default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间'
) comment '题目表' charset = utf8mb4;
```

## Excel文件格式要求

### 问答题工作表
| 列名 | 说明 | 示例 |
|------|------|------|
| 题库名称 | 游戏分类 | 游戏知识问答测试题库 |
| 问题 | 题目内容 | 请解释《我的世界》中的"红石计算机"概念。 |
| 配图 | 图片路径或URL | /path/to/image.jpg |
| 题目难度 | 简单/中等/困难 | 困难 |
| 正确答案 | 答案内容 | 红石计算机是玩家使用... |
| 答案解析 | 解析说明 | 红石计算机是玩家用... |

### 选择题工作表
| 列名 | 说明 | 示例 |
|------|------|------|
| 题库 | 游戏分类 | 游戏知识问答测试题库 |
| 问题 | 题目内容 | 《对马岛之魂》的主角是？ |
| 配图 | 图片路径或URL | /path/to/image.jpg |
| 题目难度 | 简单/中等/困难 | 困难 |
| 选项 | 选项列表 | A：境井仁【答案】\nB：志村\nC：政子\nD：龙三 |
| 答案解析 | 解析说明 | 《对马岛之魂》的主角是武士境井仁。 |

### 判断题工作表
| 列名 | 说明 | 示例 |
|------|------|------|
| 题库 | 游戏分类 | 游戏知识问答测试题库 |
| 问题 | 题目内容 | 《王者荣耀》是5v5 MOBA游戏。 |
| 配图 | 图片路径或URL | /path/to/image.jpg |
| 题目难度 | 简单/中等/困难 | 困难 |
| 正确答案 | 是/否 | 是 |
| 答案解析 | 解析说明 | 《王者荣耀》确实是5v5的多人在线战术竞技游戏。 |

## 使用方法

### 1. 环境准备

```bash
# 激活虚拟环境
.venv\Scripts\activate

# 安装依赖
pip install pandas openpyxl Pillow requests
```

### 2. 运行处理器

```bash
python quiz_data_processor.py
```

### 3. 查看结果

- **SQL文件**：`quiz_questions_insert.sql`
- **处理日志**：`quiz_processor.log`
- **错误记录**：`quiz_processor_errors.json`

## 选项格式说明

### 标准格式（推荐）
```
A：选项内容【答案】
B：选项内容
C：选项内容
D：选项内容
```

### 无标记格式
```
A：选项内容
B：选项内容
C：选项内容
D：选项内容
```
*注：无标记时会从答案解析中智能推断正确答案*

## JSON选项格式

生成的选择题options字段格式：
```json
{
  "options": [
    {"key": "A", "value": "境井仁"},
    {"key": "B", "value": "志村"},
    {"key": "C", "value": "政子"},
    {"key": "D", "value": "龙三"}
  ],
  "correct_answer": "A"
}
```

## 图片处理

### 支持的图片格式
- JPEG (.jpg, .jpeg)
- PNG (.png)
- GIF (.gif)
- BMP (.bmp)
- WEBP (.webp)

### 图片处理流程
1. **提取**：从Excel单元格中提取图片数据
2. **验证**：检查格式、尺寸、文件大小
3. **压缩**：自动压缩过大的图片
4. **上传**：上传到OSS并获取URL

### OSS配置

在`common/configs.py`中配置：
```python
OSS_ACCESS_KEY_ID = "your_access_key_id"
OSS_ACCESS_KEY_SECRET = "your_access_key_secret"
```

## 处理统计

运行完成后会显示详细统计：
- 总处理记录数
- 错误记录数
- 图片处理成功数
- 图片处理错误数
- OSS上传统计

## 错误处理

### 常见错误及解决方案

1. **Excel文件不存在**
   - 确保`游戏知识问答测试题库.xlsx`在当前目录

2. **选项格式错误**
   - 检查选项是否按标准格式编写
   - 确保使用中文冒号`：`

3. **图片处理失败**
   - 检查图片文件是否存在
   - 确认图片格式是否支持

4. **OSS上传失败**
   - 检查OSS配置是否正确
   - 确认网络连接正常

## 自定义配置

### 修改难度映射
```python
self.difficulty_mapping = {
    '简单': '简单',
    '中等': '中等', 
    '困难': '困难',
    '难': '困难',
    '易': '简单'
}
```

### 修改题型映射
```python
self.question_type_mapping = {
    '问答题': '问答题',
    '选择题': '选择题',
    '判断题': '判断题'
}
```

## 注意事项

1. **数据备份**：处理前请备份原始Excel文件
2. **编码问题**：确保Excel文件使用UTF-8编码
3. **内存使用**：大量图片处理时注意内存使用
4. **网络连接**：OSS上传需要稳定的网络连接
5. **权限检查**：确保有OSS写入权限

## 技术支持

如遇问题，请检查：
1. 日志文件：`quiz_processor.log`
2. 错误记录：`quiz_processor_errors.json`
3. 控制台输出信息

## 版本历史

- **v1.0.0**：基础功能实现
- **v1.1.0**：增加智能答案识别
- **v1.2.0**：完善图片处理功能
- **v1.3.0**：优化OSS集成和错误处理

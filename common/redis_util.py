import redis
from typing import Optional, Any


class RedisClient:
    def __init__(self, host: str, port: int, db: int):
        """
        Initialize Redis client connection.

        :param host: Redis server host
        :param port: Redis server port
        :param db: Redis database number
        """
        self.redis_client = redis.Redis(
            host=host,
            port=port,
            db=db,
            decode_responses=True  # Automatically decode responses to strings
        )

    def exists(self, key: str) -> bool:
        """
        Check if a key exists in Redis.

        :param key: Key to check
        :return: True if key exists, False otherwise
        """
        return self.redis_client.exists(key) == 1

    def get(self, key: str) -> Optional[str]:
        """
        Get the value of a key from Redis.

        :param key: Key to get
        :return: Value if key exists, None otherwise
        """
        value = self.redis_client.get(key)
        return value if value is not None else None

    def set(self, key: str, value: Any, ex: Optional[int] = None) -> bool:
        """
        Set a key-value pair in Redis.

        :param key: Key to set
        :param value: Value to set
        :param ex: Optional expiration time in seconds
        :return: True if successful
        """
        return self.redis_client.set(key, value, ex=ex)

    def delete(self, key: str) -> bool:
        """
        Delete a key from Redis.

        :param key: Key to delete
        :return: True if key was deleted, False if key didn't exist
        """
        return self.redis_client.delete(key) == 1

    def expire(self, key: str, seconds: int) -> bool:
        """
        Set an expiration time for a key.

        :param key: Key to set expiration for
        :param seconds: Expiration time in seconds
        :return: True if timeout was set, False if key doesn't exist
        """
        return self.redis_client.expire(key, seconds)

    def close(self) -> None:
        """
        Close the Redis connection.
        """
        self.redis_client.close()

    def __enter__(self):
        """Support for context manager protocol (with statement)"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Support for context manager protocol (with statement)"""
        self.close()

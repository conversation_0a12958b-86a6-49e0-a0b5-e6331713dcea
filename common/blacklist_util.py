import time
import traceback

import requests
from bs4 import BeautifulSoup

from common import notify_util


class KejinBlacklistAPI:
    """
    氪金联盟黑名单查询接口 Python 封装。
    自动带入默认 cookies，无需外部传入。

    支持的查询类型（type 参数）：
      - 'id_no'：身份证号
      - 'game_account'：游戏账号
      - 'mobile'：手机号
      - 'user_name'：姓名

    方法示例:
        api = KejinBlacklistAPI()
        data = api.search('user_name', '成龙')
    """

    ALLOWED_TYPES = {'id_no', 'game_account', 'mobile', 'user_name'}
    DEFAULT_COOKIES = {
        'account': 'jRz4Gw6zqO4BudOJljYiav7Wri7eh6hzPH97dse7',
        'HMACCOUNT': '8F76CD1F5C3E5CBE',
        # 可以继续添加其他固定 cookie
    }

    def __init__(
            self,
            base_url: str = "https://kejinlianmeng.com",
            headers: dict = None,
            cookies: dict = None,
    ):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()

        # 默认请求头
        default_headers = {
            'Accept': 'text/html, */*; q=0.01',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
            'Referer': f"{self.base_url}/account/blacklist/search",
            'X-Requested-With': 'XMLHttpRequest',
            'User-Agent': (
                'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) '
                'AppleWebKit/537.36 (KHTML, like Gecko) '
                'Chrome/********* Safari/537.36'
            ),
        }
        if headers:
            default_headers.update(headers)
        self.session.headers.update(default_headers)

        # 自动加载默认 cookies
        self.session.cookies.update(self.DEFAULT_COOKIES)
        # 如传入自定义 cookies，可覆盖默认
        if cookies:
            self.session.cookies.update(cookies)

    def fetch_html(
            self,
            search_type: str,
            keyword: str,
            page: int = 1,
            pagesize: int = 15,
            pjax_container: str = '#main'
    ) -> str:
        """
        发起请求并返回原始 HTML。

        :param search_type: 查询类型，必须为 ALLOWED_TYPES 中的一项
        :param keyword: 查询关键字
        :param page: 页码，默认为 1
        :param pagesize: 分页数量，默认为 15
        :param pjax_container: PJAX 容器标识
        :raises ValueError: 当 search_type 无效时抛出
        :return: HTML 文本
        """
        if search_type not in self.ALLOWED_TYPES:
            raise ValueError(f"Invalid search_type '{search_type}'.")

        url = f"{self.base_url}/account/blacklist/search"
        params = {
            'type': search_type,
            'keyword': keyword,
            'page': page,
            'pagesize': pagesize,
            '_pjax': pjax_container,
        }
        resp = self.session.get(url, params=params)
        resp.raise_for_status()
        return resp.text

    @staticmethod
    def parse_html(html: str) -> list:
        """
        将 HTML 解析为 Python list of dict，字段: source, game, platform, reason, time
        """
        soup = BeautifulSoup(html, 'html.parser')
        table = soup.find('table', attrs={'lay-skin': 'row'})
        if not table:
            return []

        entries = []
        for tr in table.find('tbody').find_all('tr'):
            cols = tr.find_all('td')
            if len(cols) < 5:
                continue
            entries.append({
                'source': cols[0].get_text(strip=True),
                'game': cols[1].get_text(strip=True),
                'platform': cols[2].get_text(strip=True),
                'reason': cols[3].get_text(strip=True),
                'time': cols[4].get_text(strip=True),
            })
        return entries

    def search(
            self,
            search_type: str,
            keyword: str,
            page: int = 1,
            pagesize: int = 15,
            pjax_container: str = '#main'
    ) -> list:
        """
        发起请求并返回结构化结果。

        :return: list of dict，每项包含 source, game, platform, reason, time
        """
        if len(keyword) < 6:
            return []

        # 重试配置
        max_retries = 3  # 总共尝试3次（包括首次）
        retry_delay = 1.5  # 重试间隔1.5秒

        for attempt in range(max_retries):
            try:
                html = self.fetch_html(search_type, keyword, page, pagesize, pjax_container)
                return self.parse_html(html)
            except Exception as e:
                error_detail = traceback.format_exc()

                # 如果不是最后一次尝试，记录重试信息并等待
                if attempt < max_retries - 1:
                    print(f"氪金联盟黑号查询失败，第{attempt + 1}次尝试，{retry_delay}秒后重试...")
                    time.sleep(retry_delay)
                else:
                    # 所有重试都失败，发送错误通知
                    retry_info = f"经过{max_retries}次尝试后仍然失败"
                    full_error_detail = f"{retry_info}\n最后一次错误详情：\n{error_detail}"
                    notify_util.send_notify('氪金联盟黑号查询出错', full_error_detail)

        return []


if __name__ == '__main__':
    api = KejinBlacklistAPI()
    results = api.search('game_account', '**********')
    print(results)

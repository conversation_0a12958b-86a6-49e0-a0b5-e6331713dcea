import mysql.connector
from dbutils.pooled_db import PooledDB
from mysql.connector import Error

# 创建数据库连接配置
dbconfig = {
    "host": "rm-bp1tw25fpt10tnxz2yo.rwlb.rds.aliyuncs.com",
    "user": "kkmall_online_rw0512",
    "password": "Ie3mUJMxTMjJJgVi",
    "database": "mall",
    "charset": "utf8mb4",  # 可以根据需要调整字符集
    "connection_timeout": 30,  # 连接超时设置为30秒
}

# 创建连接池
pool = PooledDB(
    creator=mysql.connector,  # 使用 mysql.connector 作为数据库驱动
    maxconnections=5,  # 连接池允许的最大连接数
    mincached=2,  # 初始化时，连接池中至少创建的空闲连接数
    maxcached=5,  # 连接池中最多空闲连接数
    maxshared=3,  # 连接池中最多共享连接数量
    blocking=True,  # 连接池中如果没有可用连接后是否阻塞
    **dbconfig
)


def get_sql_conn():
    """
    获取数据库连接
    """
    # 从连接池获取连接
    conn = pool.connection()
    cursor = conn.cursor()
    return conn, cursor


def execute_sql(sql, params=None):
    conn, cursor = get_sql_conn()  # 从连接池获取连接
    try:
        # 格式化参数到 SQL 语句中
        if params:
            formatted_sql = sql % tuple(repr(param) for param in params)
        else:
            formatted_sql = sql

        # 打印完整的 SQL 语句
        print("执行SQL:", formatted_sql)

        cursor.execute(sql, params or ())  # 执行SQL语句
        if cursor.with_rows:
            result = cursor.fetchall()  # 获取所有记录
        else:
            conn.commit()  # 提交事务
            result = []
        return result
    except Error as e:
        conn.rollback()
        print("执行SQL过程中出现错误：", e)
        return []
    finally:
        cursor.close()
        conn.close()


def get_index_dict(cursor):
    """
    获取数据库对应表中的字段名
    """
    index_dict = dict()
    index = 0
    for desc in cursor.description:
        index_dict[desc[0]] = index
        index = index + 1
    return index_dict


def get_dict_data_sql(cursor, sql, params=None):
    """
    运行sql语句，获取结果，并根据表中字段名，转化成dict格式（默认是tuple格式）
    """
    cursor.execute(sql, params or ())
    data = cursor.fetchall()
    index_dict = get_index_dict(cursor)
    res = []
    for datai in data:
        resi = dict()
        for indexi in index_dict:
            resi[indexi] = datai[index_dict[indexi]]
        res.append(resi)
    return res


def execute_sql_dict(sql, params=None):
    con, cursor = get_sql_conn()
    return get_dict_data_sql(cursor, sql, params)


def get_product_id_by_product_sn(product_sn):
    """
    根据 product_sn 获取 product_id
    """
    sql = "SELECT id FROM pms_product WHERE product_sn = %s"
    res = execute_sql(sql, (product_sn,))
    if res:
        return res[0][0]
    else:
        return None


def get_member_id_by_username(username):
    """
    根据 username 获取 member_id
    """
    sql = "SELECT id FROM ums_member WHERE username = %s"
    res = execute_sql(sql, (username,))
    if res:
        return res[0][0]
    else:
        return None


def get_username_by_member_id(member_id):
    """
    根据 member_id 获取 username
    """
    sql = "SELECT username FROM ums_member WHERE id = %s"
    res = execute_sql(sql, (member_id,))
    if res:
        return res[0][0]
    else:
        return None


def is_no_import_member_exit(username):
    """
    检查非导入用户是否已存在
    :param username:
    :return:
    """
    query = "SELECT * FROM ums_member where username=%s and is_import=0"
    res = execute_sql(query, (username,))
    if len(res) > 0:
        return True
    else:
        return False


def get_import_member_id(username):
    """
    获取导入用户的id
    """
    query = "SELECT id FROM ums_import_member where username=%s"
    res = execute_sql(query, (username,))
    if res:
        return res[0][0]
    else:
        return None


def get_category_map():
    """
    获取分类映射
    """
    sql = "SELECT id, name FROM pms_product_category WHERE parent_id = 74"
    res = execute_sql(sql)
    category_map = {}
    for row in res:
        category_map[row[1]] = row[0]
    return category_map


def get_category_id_by_flag_id(flag_id):
    query_category_sql = "SELECT category_id FROM pms_import_product WHERE flag_id = %s limit 1;"
    category_id = execute_sql_dict(query_category_sql, (flag_id,))[0]['category_id']
    return category_id


def main():
    con, cursor = get_sql_conn()
    sql = "SELECT * FROM pms_product limit 1"
    print(get_dict_data_sql(cursor, sql))


if __name__ == '__main__':
    r = get_product_id_by_product_sn('NSH2522')

    print(r)

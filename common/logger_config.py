# common/logger.py
import logging
import os
from logging.handlers import TimedRotatingFileHandler

# 日志格式
LOG_FORMAT = "%(asctime)s - %(levelname)s - %(name)s - %(message)s"

# 日志文件路径
LOG_DIR = "logs"
LOG_FILE = os.path.join(LOG_DIR, "app.log")

# 确保日志目录存在
if not os.path.exists(LOG_DIR):
    os.makedirs(LOG_DIR)


def setup_logger(name: str, level: int = logging.INFO) -> logging.Logger:
    """
    配置并返回一个日志记录器。

    :param name: 日志记录器的名称
    :param level: 日志级别（默认为 logging.INFO）
    :return: 配置好的日志记录器
    """
    # 创建日志记录器
    logger = logging.getLogger(name)
    logger.setLevel(level)

    # 创建控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(level)
    console_handler.setFormatter(logging.Formatter(LOG_FORMAT))

    # 创建文件处理器（按天轮转日志文件）
    file_handler = TimedRotatingFileHandler(LOG_FILE, when="midnight", interval=1, backupCount=7)
    file_handler.setLevel(level)
    file_handler.setFormatter(logging.Formatter(LOG_FORMAT))

    # 添加处理器到日志记录器
    logger.addHandler(console_handler)
    logger.addHandler(file_handler)

    return logger

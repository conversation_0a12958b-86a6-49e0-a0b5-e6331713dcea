#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图片处理模块
功能：
1. 从Excel中提取图片
2. 上传图片到OSS
3. 返回图片URL
"""

import os
import io
import logging
from typing import Optional, Dict, Any
from PIL import Image
import requests
from oss_integration import OSSUploader

logger = logging.getLogger(__name__)


class ImageProcessor:
    """图片处理器"""
    
    def __init__(self):
        self.processed_images = {}
        self.upload_errors = []
        self.oss_uploader = OSSUploader()
    
    def extract_image_from_excel_cell(self, cell_value: Any) -> Optional[bytes]:
        """从Excel单元格中提取图片数据"""
        try:
            # 如果单元格包含图片路径
            if isinstance(cell_value, str) and cell_value.strip():
                image_path = cell_value.strip()
                
                # 检查是否为本地文件路径
                if os.path.exists(image_path):
                    with open(image_path, 'rb') as f:
                        return f.read()
                
                # 检查是否为URL
                if image_path.startswith(('http://', 'https://')):
                    try:
                        response = requests.get(image_path, timeout=10)
                        if response.status_code == 200:
                            return response.content
                    except Exception as e:
                        logger.warning(f"下载图片失败 {image_path}: {e}")
            
            # 如果是其他类型的数据，可能需要根据实际情况处理
            # 例如：openpyxl可能返回图片对象
            
            return None
            
        except Exception as e:
            logger.error(f"提取图片数据时出错: {e}")
            return None
    
    def validate_image(self, image_data: bytes) -> bool:
        """验证图片数据是否有效"""
        try:
            with Image.open(io.BytesIO(image_data)) as img:
                # 检查图片格式
                if img.format not in ['JPEG', 'PNG', 'GIF', 'BMP', 'WEBP']:
                    logger.warning(f"不支持的图片格式: {img.format}")
                    return False
                
                # 检查图片尺寸
                width, height = img.size
                if width > 5000 or height > 5000:
                    logger.warning(f"图片尺寸过大: {width}x{height}")
                    return False
                
                # 检查文件大小（5MB限制）
                if len(image_data) > 5 * 1024 * 1024:
                    logger.warning(f"图片文件过大: {len(image_data)} bytes")
                    return False
                
                return True
                
        except Exception as e:
            logger.error(f"验证图片时出错: {e}")
            return False
    
    def compress_image_if_needed(self, image_data: bytes, max_size: int = 1024*1024) -> bytes:
        """如果图片过大则压缩"""
        try:
            if len(image_data) <= max_size:
                return image_data
            
            with Image.open(io.BytesIO(image_data)) as img:
                # 转换为RGB模式（如果需要）
                if img.mode in ('RGBA', 'LA', 'P'):
                    img = img.convert('RGB')
                
                # 计算压缩比例
                quality = 85
                while quality > 20:
                    output = io.BytesIO()
                    img.save(output, format='JPEG', quality=quality, optimize=True)
                    compressed_data = output.getvalue()
                    
                    if len(compressed_data) <= max_size:
                        logger.info(f"图片压缩成功: {len(image_data)} -> {len(compressed_data)} bytes")
                        return compressed_data
                    
                    quality -= 10
                
                # 如果还是太大，尝试缩小尺寸
                width, height = img.size
                scale = 0.8
                while scale > 0.3:
                    new_width = int(width * scale)
                    new_height = int(height * scale)
                    resized_img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)
                    
                    output = io.BytesIO()
                    resized_img.save(output, format='JPEG', quality=75, optimize=True)
                    compressed_data = output.getvalue()
                    
                    if len(compressed_data) <= max_size:
                        logger.info(f"图片缩放压缩成功: {width}x{height} -> {new_width}x{new_height}, {len(image_data)} -> {len(compressed_data)} bytes")
                        return compressed_data
                    
                    scale -= 0.1
                
                # 如果还是太大，返回原数据
                logger.warning("图片压缩失败，返回原数据")
                return image_data
                
        except Exception as e:
            logger.error(f"压缩图片时出错: {e}")
            return image_data
    
    def upload_to_oss(self, image_data: bytes, file_extension: str = 'jpg') -> Optional[str]:
        """上传图片到OSS"""
        try:
            # 使用OSS上传器上传图片
            image_url = self.oss_uploader.upload_image_data(image_data, file_extension)
            return image_url

        except Exception as e:
            logger.error(f"上传图片到OSS时出错: {e}")
            return None
    
    def process_image(self, cell_value: Any, question_id: str = None) -> Optional[str]:
        """处理图片的主要方法"""
        try:
            # 提取图片数据
            image_data = self.extract_image_from_excel_cell(cell_value)
            if not image_data:
                return None
            
            # 验证图片
            if not self.validate_image(image_data):
                logger.warning("图片验证失败")
                return None
            
            # 压缩图片（如果需要）
            compressed_data = self.compress_image_if_needed(image_data)
            
            # 确定文件扩展名
            file_extension = 'jpg'  # 默认为jpg，可以根据原始格式调整

            # 上传到OSS
            image_url = self.upload_to_oss(compressed_data, file_extension)
            
            if image_url:
                # 记录处理结果
                self.processed_images[question_id or len(self.processed_images)] = {
                    'original_size': len(image_data),
                    'compressed_size': len(compressed_data),
                    'url': image_url
                }
                
                return image_url
            
            return None
            
        except Exception as e:
            logger.error(f"处理图片时出错: {e}")
            self.upload_errors.append({
                'question_id': question_id,
                'error': str(e),
                'cell_value': str(cell_value)
            })
            return None
    
    def get_processing_summary(self) -> Dict[str, Any]:
        """获取处理摘要"""
        oss_stats = self.oss_uploader.get_upload_statistics()

        return {
            'total_processed': len(self.processed_images),
            'total_errors': len(self.upload_errors),
            'processed_images': self.processed_images,
            'errors': self.upload_errors,
            'oss_statistics': oss_stats
        }


def test_image_processor():
    """测试图片处理器"""
    processor = ImageProcessor()
    
    # 测试用例1：空值
    result = processor.process_image(None)
    print(f"测试空值: {result}")
    
    # 测试用例2：无效路径
    result = processor.process_image("invalid_path.jpg")
    print(f"测试无效路径: {result}")
    
    # 测试用例3：URL（如果有网络连接）
    # result = processor.process_image("https://example.com/image.jpg")
    # print(f"测试URL: {result}")
    
    # 输出处理摘要
    summary = processor.get_processing_summary()
    print(f"处理摘要: {summary}")


if __name__ == "__main__":
    test_image_processor()

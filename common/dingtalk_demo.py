#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
钉钉机器人简单使用示例
"""

from dingtalk_robot import DingTalkRobot


def main():
    # 替换为你的实际token和secret
    ACCESS_TOKEN = "08a8285420b344660b4125d7d78a65936ccd279a9b9c7bb810ae8188027a70ee"
    SECRET = "SEC9d5fa738d5a41623384485934d4365cac024c94c42e63521917a38e14d2e5742"
    
    # 创建机器人实例
    robot = DingTalkRobot(ACCESS_TOKEN, SECRET)
    
    # 发送简单文本消息
    result = robot.send_text("Hello, 这是一条测试消息！")
    print(f"发送结果: {result}")
    
    # 发送@所有人的消息
    robot.send_text("重要通知：系统将于今晚维护", at_all=True)
    
    # 发送@特定手机号的消息
    robot.send_text("请及时处理工单", at_mobiles=["13176667929"])


if __name__ == "__main__":
    main()
